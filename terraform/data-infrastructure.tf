data "terraform_remote_state" "infrastructure" {
  workspace = local.env
  backend   = "s3"
  config = {
    bucket      = "bemobi-smscorpchile-terraform-states" ###MARK_ACCOUNT_SCOPE_PARSED 
    region      = "us-east-1"
    assume_role = { role_arn = "arn:aws:iam::************:role/deployer" }    ###MARK_MAIN_ACCOUNT
    key         = "bigbangs/smscorpchile/smscorpchile/infrastructure.tfstate" ###MARK_INFRA_SCOPE_PARSED
  }
}

locals {

  infrastructure = data.terraform_remote_state.infrastructure.outputs

  aws_account_id = local.infrastructure.aws_account_id
  region         = local.infrastructure.region
  team           = local.infrastructure.team
  scope_parsed   = local.infrastructure.scope_parsed
  scope          = local.infrastructure.scope

  subnet_private_ids = local.infrastructure.subnet_private_ids
  subnet_public_ids  = local.infrastructure.subnet_public_ids

  cidr_block             = local.infrastructure.cidr_block
  private_cidr_block     = local.infrastructure.private_cidr_block
  opsgenie_sns_topic_arn = local.infrastructure.opsgenie_sns_topic_arn

  envzone_name    = local.infrastructure.envzone_name
  envzone_zone_id = local.infrastructure.envzone_zone_id

  alb_arn                   = local.infrastructure.alb_arn
  alb_arn_suffix            = local.infrastructure.alb_arn_suffix
  alb_fqdn                  = local.infrastructure.alb_fqdn
  alb_dns_name              = local.infrastructure.alb_dns_name
  alb_zone_id               = local.infrastructure.alb_zone_id
  alb_http_listener_arn     = local.infrastructure.alb_http_listener_arn
  alb_https_listener_arn    = local.infrastructure.alb_https_listener_arn
  alb_aws_security_group_id = local.infrastructure.alb_aws_security_group_id


  albext_arn                   = local.infrastructure.albext_arn
  albext_arn_suffix            = local.infrastructure.albext_arn_suffix
  albext_fqdn                  = local.infrastructure.albext_fqdn
  albext_dns_name              = local.infrastructure.albext_dns_name
  albext_zone_id               = local.infrastructure.albext_zone_id
  albext_http_listener_arn     = local.infrastructure.albext_http_listener_arn
  albext_https_listener_arn    = local.infrastructure.albext_https_listener_arn
  albext_aws_security_group_id = local.infrastructure.albext_aws_security_group_id

  albs = {
    int = {
      arn                   = local.infrastructure.alb_arn
      arn_suffix            = local.infrastructure.alb_arn_suffix
      fqdn                  = local.infrastructure.alb_fqdn
      dns_name              = local.infrastructure.alb_dns_name
      zone_id               = local.infrastructure.alb_zone_id
      https_listener_arn    = local.infrastructure.alb_https_listener_arn
      aws_security_group_id = local.infrastructure.alb_aws_security_group_id
    }

    ext = {
      arn                   = local.infrastructure.albext_arn
      arn_suffix            = local.infrastructure.albext_arn_suffix
      fqdn                  = local.infrastructure.albext_fqdn
      dns_name              = local.infrastructure.albext_dns_name
      zone_id               = local.infrastructure.albext_zone_id
      https_listener_arn    = local.infrastructure.albext_https_listener_arn
      aws_security_group_id = local.infrastructure.albext_aws_security_group_id
    }

  }


  business_group = local.infrastructure.business_group
  business_unit  = local.infrastructure.business_unit
  squad          = local.infrastructure.squad
  vpc_id         = data.terraform_remote_state.infrastructure.outputs.vpc_id


}