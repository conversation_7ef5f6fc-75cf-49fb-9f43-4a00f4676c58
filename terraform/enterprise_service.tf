locals {
  enterprise_apis = {
    "enterprise-account-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/account-cl/health"
      enable_efs          = false #true para habilitar EFS
      efs_mount_path      = "/mnt/shared"
      enable_auto_scaling = false
    },
    "enterprise-general-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/general-cl/health"
      enable_efs          = false # TRUE para habilitar EFS
      enable_auto_scaling = false
    },
    "enterprise-blacklist-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/blacklist-cl/health"
      enable_efs          = false # true para habilitar EFS
      enable_auto_scaling = false
    },
    "enterprise-webservice-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/ws-enterprise-cl/health"
      enable_efs          = false # true para habilitar EFS
      enable_auto_scaling = true
    },
    "enterprise-report-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/report-cl/health"
      enable_efs          = true # EFS para generar reportes
      efs_mount_path      = "/reports"
      enable_auto_scaling = false
    },
    "enterprise-maintenance-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/actuator/health"
      enable_efs          = false # true para habilitar EFS
      enable_auto_scaling = false
    },
    "common-shorturl-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 8080
      health_check_url    = "/shorturl/actuator/health"
      enable_efs          = false # true para habilitar EFS
      enable_auto_scaling = false
    },
    "common-mailer-api" = {
      image               = "ubuntu"
      cpu                 = "256"
      memory              = "512"
      ports               = 80 # se coloca en 80 debido a que se usa nginx para validar
      health_check_url    = "/health"
      enable_efs          = false # true para habilitar EFS
      enable_auto_scaling = false
    }
  }
}

module "java_api_services" {
  for_each = local.enterprise_apis
  source   = "./modules/api-service"

  name             = each.key
  image            = each.value.image
  cpu              = each.value.cpu
  memory           = each.value.memory
  application_port = each.value.ports
  health_check_url = each.value.health_check_url

  vpc_id     = local.vpc_id
  subnet_ids = local.subnet_private_ids

  cluster_name       = local.cluster_name
  alb_listener_arn   = local.albext_https_listener_arn
  envzone_name       = local.envzone_name
  envzone_zone_id    = local.envzone_zone_id
  alb_dns_name       = local.albext_dns_name
  private_cidr_block = "10.0.0.0/8"
  log_retention_days = local.retention_in_days

  # EFS Configuration (optional per API) revis<r mas adelante lo de efs_mount_path
  enable_efs     = lookup(each.value, "enable_efs", false)
  efs_mount_path = lookup(each.value, "efs_mount_path", "/reports")

  # Auto Scaling Configuration
  enable_auto_scaling = lookup(each.value, "enable_auto_scaling", false)
  min_capacity        = local.min_capacity_value[local.env]
  max_capacity        = local.max_capacity_value[local.env]
  desired_capacity    = local.desired_capacity_value[local.env]
  cpu_target_value    = local.cpu_target_value[local.env]
  memory_target_value = local.mem_target_value[local.env]
  scale_in_cooldown   = local.scale_in_cooldown_value[local.env]
  scale_out_cooldown  = local.scale_out_cooldown_value[local.env]
}


