
output "task_definition_arn" {
  value = aws_ecs_task_definition.td.arn
}

output "efs_file_system_id" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].id : null
  description = "EFS file system ID (if EFS is enabled)"
}

output "efs_file_system_arn" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].arn : null
  description = "EFS file system ARN (if EFS is enabled)"
}

output "efs_dns_name" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].dns_name : null
  description = "EFS DNS name (if EFS is enabled)"
}

# Auto Scaling Outputs
output "autoscaling_target_resource_id" {
  value       = var.enable_auto_scaling ? aws_appautoscaling_target.ecs_target[0].resource_id : null
  description = "Auto scaling target resource ID"
}

output "cpu_autoscaling_policy_arn" {
  value       = var.enable_auto_scaling ? aws_appautoscaling_policy.ecs_policy_cpu[0].arn : null
  description = "CPU auto scaling policy ARN"
}

output "memory_autoscaling_policy_arn" {
  value       = var.enable_auto_scaling ? aws_appautoscaling_policy.ecs_policy_memory[0].arn : null
  description = "Memory auto scaling policy ARN"
}

output "service_name" {
  value       = aws_ecs_service.svc.name
  description = "ECS service name"
}

output "target_group_arn" {
  value       = aws_lb_target_group.tg.arn
  description = "Load balancer target group ARN"
}
