# TODO: change variables
# Terraform:
terraform {
  required_version = ">= 1.5.0"
  required_providers { aws = { version = "~> 5.0" } }
  # d Point to the bucket created on the prd account
  backend "s3" {
    bucket      = "bemobi-smscorpchile-terraform-states" ###MARK_ACCOUNT_SCOPE_PARSED
    region      = "us-east-1"
    acl         = "bucket-owner-full-control"
    assume_role = { role_arn = "arn:aws:iam::************:role/deployer" } ###MARK_MAIN_ACCOUNT
    key         = "backend-enterprise-aws-infra.tfstate"                   ###MARK_INFRA_SCOPE_PARSED
  }
}

